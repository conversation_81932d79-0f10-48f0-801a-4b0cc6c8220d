#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دليل مكتبتي الذكية - SmartEdu Library
إنشاء ملف PDF شامل باللغة العربية
"""

from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_RIGHT, TA_CENTER
import os

def create_smart_library_guide():
    """إنشاء دليل المكتبة الذكية"""
    
    # إعداد الملف
    filename = "دليل_مكتبتي_الذكية_SmartEdu.pdf"
    doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)
    
    # قائمة المحتوى
    story = []
    
    # الأنماط
    styles = getSampleStyleSheet()
    
    # نمط العنوان الرئيسي
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor='#2563eb'
    )
    
    # نمط العنوان الفرعي
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=18,
        spaceAfter=12,
        alignment=TA_RIGHT,
        textColor='#1d4ed8'
    )
    
    # نمط النص العادي
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        alignment=TA_RIGHT,
        leading=18
    )
    
    # نمط القائمة
    bullet_style = ParagraphStyle(
        'CustomBullet',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=3,
        alignment=TA_RIGHT,
        leftIndent=20,
        bulletIndent=10
    )
    
    # العنوان الرئيسي
    story.append(Paragraph("مكتبتي الذكية", title_style))
    story.append(Paragraph("SmartEdu Library", title_style))
    story.append(Spacer(1, 20))
    story.append(Paragraph("دليل شامل للمكتبة الإلكترونية الذكية", heading_style))
    story.append(Paragraph("مدرسة White Land", normal_style))
    story.append(Spacer(1, 30))
    
    # المقدمة
    story.append(Paragraph("نظرة عامة على المشروع", heading_style))
    intro_text = """
    مكتبة إلكترونية ذكية شاملة تهدف إلى تطوير التعليم من خلال التكنولوجيا الحديثة والذكاء الاصطناعي.
    تحتوي المكتبة على جميع المواد الدراسية لكافة المراحل التعليمية مع أدوات تفاعلية متطورة.
    """
    story.append(Paragraph(intro_text, normal_style))
    story.append(Spacer(1, 20))
    
    # المراحل التعليمية
    story.append(Paragraph("المراحل التعليمية المتاحة", heading_style))
    
    stages = [
        "المرحلة الابتدائية: من الصف الأول إلى السادس الابتدائي",
        "المرحلة الإعدادية: من الصف الأول إلى الثالث الإعدادي", 
        "المرحلة الثانوية: من الصف الأول إلى الثالث الثانوي"
    ]
    
    for stage in stages:
        story.append(Paragraph(f"• {stage}", bullet_style))
    
    story.append(Spacer(1, 20))
    
    # المواد الدراسية
    story.append(Paragraph("المواد الدراسية", heading_style))
    
    subjects = [
        "اللغة العربية: قواعد اللغة والأدب والتعبير",
        "الرياضيات: الحساب والجبر والهندسة",
        "العلوم: الفيزياء والكيمياء والأحياء",
        "اللغة الإنجليزية: قواعد ومحادثة ومفردات",
        "تكنولوجيا المعلومات: الكمبيوتر والبرمجة والتكنولوجيا"
    ]
    
    for subject in subjects:
        story.append(Paragraph(f"• {subject}", bullet_style))
    
    story.append(Spacer(1, 20))
    
    # أنواع المحتوى
    story.append(Paragraph("أنواع المحتوى المتاح", heading_style))
    
    content_types = [
        "كتب PDF: جميع الكتب المدرسية بصيغة عالية الجودة",
        "فيديوهات تعليمية: شروحات مرئية مبسطة لجميع الدروس",
        "تمارين تفاعلية: أسئلة متنوعة لتقييم مستوى الطالب",
        "اختبارات قصيرة: تقييم سريع للفهم والاستيعاب",
        "ملخصات ذكية: نقاط مهمة لكل درس",
        "معلم ذكي: مساعد بالذكاء الاصطناعي للإجابة على الأسئلة"
    ]
    
    for content in content_types:
        story.append(Paragraph(f"• {content}", bullet_style))
    
    story.append(PageBreak())
    
    # المثال العملي
    story.append(Paragraph("المثال العملي: الصف الخامس الابتدائي", heading_style))
    
    example_text = """
    تم تطبيق مثال كامل للصف الخامس الابتدائي لغات يشمل مادتي اللغة الإنجليزية وتكنولوجيا المعلومات
    مع جميع أنواع المحتوى والأدوات التفاعلية.
    """
    story.append(Paragraph(example_text, normal_style))
    story.append(Spacer(1, 15))
    
    # مادة اللغة الإنجليزية
    story.append(Paragraph("مادة اللغة الإنجليزية", heading_style))
    
    english_content = [
        "كتاب الطالب - Connect Plus 5 (كتاب الوزارة الرسمي)",
        "كتاب التمارين - Workbook (كتاب التدريبات)",
        "كتاب المعاصر - English (مرجع إضافي)",
        "فيديوهات شرح للوحدة الأولى: My Family",
        "فيديوهات شرح للوحدة الثانية: My School",
        "تمارين تفاعلية على الضمائر والمفردات",
        "معلم ذكي متخصص في اللغة الإنجليزية"
    ]
    
    for item in english_content:
        story.append(Paragraph(f"• {item}", bullet_style))
    
    story.append(Spacer(1, 15))
    
    # مادة تكنولوجيا المعلومات
    story.append(Paragraph("مادة تكنولوجيا المعلومات", heading_style))
    
    ict_content = [
        "كتاب تكنولوجيا المعلومات - الصف الخامس (كتاب الوزارة)",
        "دليل المعلم - ICT",
        "فيديوهات عن أجزاء الحاسوب الأساسية",
        "شرح برنامج الرسام - Paint",
        "تمارين على مكونات الحاسوب",
        "معلم ذكي متخصص في الكمبيوتر والتكنولوجيا"
    ]
    
    for item in ict_content:
        story.append(Paragraph(f"• {item}", bullet_style))
    
    story.append(Spacer(1, 20))
    
    # مميزات الذكاء الاصطناعي
    story.append(Paragraph("مميزات الذكاء الاصطناعي", heading_style))
    
    ai_features = [
        "شرح الدروس بأسلوب مبسط حسب عمر الطالب",
        "تلخيص المحتوى في نقاط واضحة",
        "توليد أسئلة تفاعلية متنوعة",
        "تقديم اختبارات ذكية تتكيف مع مستوى الطالب",
        "الإجابة على استفسارات الطالب كمعلم خاص",
        "اقتراح خطط مذاكرة شخصية"
    ]
    
    for feature in ai_features:
        story.append(Paragraph(f"• {feature}", bullet_style))
    
    story.append(PageBreak())
    
    # التقنيات المستخدمة
    story.append(Paragraph("التقنيات والأدوات المستخدمة", heading_style))
    
    technologies = [
        "Next.js 14: إطار عمل React متطور للواجهات",
        "React 18: مكتبة JavaScript لبناء واجهات المستخدم",
        "TypeScript: لغة برمجة قوية ومرنة",
        "Tailwind CSS: إطار عمل CSS للتصميم السريع",
        "Lucide React: مكتبة أيقونات حديثة",
        "خطوط عربية: Cairo و Amiri للنصوص العربية"
    ]
    
    for tech in technologies:
        story.append(Paragraph(f"• {tech}", bullet_style))
    
    story.append(Spacer(1, 20))
    
    # خطوات التشغيل
    story.append(Paragraph("خطوات تشغيل المشروع", heading_style))
    
    steps = [
        "تثبيت Node.js على الجهاز",
        "تحميل ملفات المشروع",
        "تشغيل الأمر: npm install",
        "تشغيل الأمر: npm run dev",
        "فتح المتصفح على: http://localhost:3000"
    ]
    
    for i, step in enumerate(steps, 1):
        story.append(Paragraph(f"{i}. {step}", bullet_style))
    
    story.append(Spacer(1, 20))
    
    # الخطوات المستقبلية
    story.append(Paragraph("خطة التطوير المستقبلية", heading_style))
    
    future_plans = [
        "ربط API حقيقي للذكاء الاصطناعي (ChatGPT)",
        "إضافة نظام تسجيل الدخول للطلاب والمعلمين",
        "تطوير لوحة تحكم للمعلمين وأولياء الأمور",
        "إضافة المزيد من المراحل والمواد الدراسية",
        "تطوير تطبيق موبايل للمكتبة",
        "إضافة نظام التقارير والإحصائيات",
        "دعم التعلم الجماعي والمناقشات",
        "إضافة ألعاب تعليمية تفاعلية"
    ]
    
    for plan in future_plans:
        story.append(Paragraph(f"• {plan}", bullet_style))
    
    story.append(Spacer(1, 30))
    
    # الخاتمة
    story.append(Paragraph("الخاتمة", heading_style))
    conclusion_text = """
    مكتبتي الذكية تمثل نقلة نوعية في التعليم الإلكتروني، حيث تجمع بين المحتوى التعليمي عالي الجودة
    وتقنيات الذكاء الاصطناعي لتوفير تجربة تعلم متميزة وشخصية لكل طالب.
    
    المشروع قابل للتطوير والتوسع ليشمل جميع المراحل التعليمية والمواد الدراسية،
    مما يجعله حلاً شاملاً لاحتياجات التعليم الحديث.
    """
    story.append(Paragraph(conclusion_text, normal_style))
    
    story.append(Spacer(1, 20))
    story.append(Paragraph("© 2024 مدرسة White Land - جميع الحقوق محفوظة", normal_style))
    story.append(Paragraph("تم التطوير بتقنيات حديثة لخدمة التعليم", normal_style))
    
    # إنشاء الملف
    doc.build(story)
    print(f"تم إنشاء الملف: {filename}")
    return filename

if __name__ == "__main__":
    create_smart_library_guide()
