'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { ArrowR<PERSON>, BookOpen, FileText, Play, Brain, Award, Download, Clock, Star, Users } from 'lucide-react'

export default function GradePage() {
  const params = useParams()
  const level = params.level as string
  const grade = params.grade as string

  const gradeData = {
    primary: {
      name: 'المرحلة الابتدائية',
      grades: {
        '5': {
          name: 'الصف الخامس الابتدائي',
          description: 'محتوى تعليمي متقدم للصف الخامس',
          subjects: [
            {
              id: 'english',
              name: 'اللغة الإنجليزية',
              description: 'منهج Connect Plus للصف الخامس',
              color: 'from-purple-400 to-purple-600',
              icon: '🇬🇧',
              lessons: 12,
              videos: 8,
              exercises: 15
            },
            {
              id: 'ict',
              name: 'تكنولوجيا المعلومات',
              description: 'أساسيات الكمبيوتر والبرمجة',
              color: 'from-orange-400 to-orange-600',
              icon: '💻',
              lessons: 10,
              videos: 6,
              exercises: 12
            }
          ]
        }
      }
    }
  }

  const currentGrade = gradeData[level as keyof typeof gradeData]?.grades[grade as keyof typeof gradeData.primary.grades]

  if (!currentGrade) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">الصف غير متاح حالياً</h2>
          <p className="text-gray-600 mb-6">نعمل على إضافة المحتوى قريباً</p>
          <Link href={`/levels/${level}`} className="btn-primary">
            العودة للمرحلة
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-lg">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Link href={`/levels/${level}`} className="flex items-center text-primary-600 hover:text-primary-700">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للمرحلة
            </Link>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <BookOpen className="h-8 w-8 text-primary-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-800">{currentGrade.name}</h1>
                <p className="text-gray-600 text-sm">مدرسة White Land</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Grade Info */}
      <section className="py-12 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">{currentGrade.name}</h2>
          <p className="text-xl text-gray-600 mb-8">{currentGrade.description}</p>
          
          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="text-3xl font-bold text-primary-600 mb-2">
                {currentGrade.subjects.reduce((acc, subject) => acc + subject.lessons, 0)}
              </div>
              <div className="text-gray-600">درس متاح</div>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {currentGrade.subjects.reduce((acc, subject) => acc + subject.videos, 0)}
              </div>
              <div className="text-gray-600">فيديو تعليمي</div>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {currentGrade.subjects.reduce((acc, subject) => acc + subject.exercises, 0)}
              </div>
              <div className="text-gray-600">تمرين تفاعلي</div>
            </div>
          </div>
        </div>
      </section>

      {/* Subjects */}
      <section className="py-12 px-4">
        <div className="container mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8 text-gray-800">المواد الدراسية</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {currentGrade.subjects.map((subject) => (
              <div key={subject.id} className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                <div className={`bg-gradient-to-r ${subject.color} p-8 text-white`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-4xl">{subject.icon}</div>
                    <div className="text-right">
                      <h4 className="text-2xl font-bold">{subject.name}</h4>
                      <p className="text-white/80">{subject.description}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 mt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{subject.lessons}</div>
                      <div className="text-sm text-white/80">درس</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{subject.videos}</div>
                      <div className="text-sm text-white/80">فيديو</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{subject.exercises}</div>
                      <div className="text-sm text-white/80">تمرين</div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <Link href={`/levels/${level}/grade/${grade}/subject/${subject.id}/books`}>
                      <div className="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg text-center transition-colors cursor-pointer">
                        <FileText className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                        <div className="font-semibold text-blue-800">الكتب</div>
                        <div className="text-sm text-blue-600">PDF</div>
                      </div>
                    </Link>
                    
                    <Link href={`/levels/${level}/grade/${grade}/subject/${subject.id}/videos`}>
                      <div className="bg-green-50 hover:bg-green-100 p-4 rounded-lg text-center transition-colors cursor-pointer">
                        <Play className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <div className="font-semibold text-green-800">الفيديوهات</div>
                        <div className="text-sm text-green-600">شروحات</div>
                      </div>
                    </Link>
                    
                    <Link href={`/levels/${level}/grade/${grade}/subject/${subject.id}/exercises`}>
                      <div className="bg-orange-50 hover:bg-orange-100 p-4 rounded-lg text-center transition-colors cursor-pointer">
                        <Award className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                        <div className="font-semibold text-orange-800">التمارين</div>
                        <div className="text-sm text-orange-600">تفاعلية</div>
                      </div>
                    </Link>
                    
                    <Link href={`/levels/${level}/grade/${grade}/subject/${subject.id}/ai-tutor`}>
                      <div className="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg text-center transition-colors cursor-pointer">
                        <Brain className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                        <div className="font-semibold text-purple-800">المعلم الذكي</div>
                        <div className="text-sm text-purple-600">AI</div>
                      </div>
                    </Link>
                  </div>
                  
                  <Link href={`/levels/${level}/grade/${grade}/subject/${subject.id}`}>
                    <button className="w-full bg-gradient-to-r from-primary-500 to-purple-600 text-white font-bold py-3 px-6 rounded-lg hover:from-primary-600 hover:to-purple-700 transition-all duration-200">
                      ادخل إلى المادة
                    </button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Quick Access */}
      <section className="py-12 px-4 bg-white">
        <div className="container mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8 text-gray-800">وصول سريع</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
              <Download className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h4 className="font-bold text-blue-800 mb-2">تحميل الكتب</h4>
              <p className="text-blue-600 text-sm">جميع كتب المنهج بصيغة PDF</p>
            </div>
            
            <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
              <Clock className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h4 className="font-bold text-green-800 mb-2">جدول المراجعة</h4>
              <p className="text-green-600 text-sm">خطة مذاكرة مخصصة</p>
            </div>
            
            <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
              <Star className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h4 className="font-bold text-purple-800 mb-2">المفضلة</h4>
              <p className="text-purple-600 text-sm">الدروس المحفوظة</p>
            </div>
            
            <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl">
              <Users className="h-12 w-12 text-orange-600 mx-auto mb-4" />
              <h4 className="font-bold text-orange-800 mb-2">مجموعة الدراسة</h4>
              <p className="text-orange-600 text-sm">تعلم مع الأصدقاء</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
