'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { ArrowRight, BookOpen, Play, FileText, Download, ExternalLink, Clock, Star, Award, Brain } from 'lucide-react'

export default function SubjectPage() {
  const params = useParams()
  const level = params.level as string
  const grade = params.grade as string
  const subject = params.subject as string

  // بيانات المحتوى للصف الخامس الابتدائي - مثال عملي
  const subjectData = {
    english: {
      name: 'اللغة الإنجليزية',
      description: 'منهج Connect Plus للصف الخامس الابتدائي',
      icon: '🇬🇧',
      color: 'from-purple-400 to-purple-600',
      books: [
        {
          id: 1,
          title: 'كتاب الطالب - Connect Plus 5',
          type: 'كتاب الوزارة الرسمي',
          pages: 120,
          size: '15 MB',
          downloadUrl: '#',
          previewUrl: '#'
        },
        {
          id: 2,
          title: 'كتاب التمارين - Workbook',
          type: 'كتاب التدريبات',
          pages: 80,
          size: '10 MB',
          downloadUrl: '#',
          previewUrl: '#'
        },
        {
          id: 3,
          title: 'كتاب المعاصر - English',
          type: 'مرجع إضافي',
          pages: 200,
          size: '25 MB',
          downloadUrl: '#',
          previewUrl: '#'
        }
      ],
      videos: [
        {
          id: 1,
          title: 'Unit 1: My Family - الوحدة الأولى',
          description: 'تعلم مفردات العائلة والضمائر الشخصية',
          duration: '15:30',
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
          youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          topics: ['Family members', 'Personal pronouns', 'Simple present']
        },
        {
          id: 2,
          title: 'Unit 2: My School - مدرستي',
          description: 'مفردات المدرسة والأدوات المدرسية',
          duration: '12:45',
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
          youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          topics: ['School subjects', 'Classroom objects', 'Prepositions']
        }
      ],
      lessons: [
        {
          id: 1,
          title: 'الدرس الأول: التعارف والتحية',
          unit: 'Unit 1',
          content: 'تعلم كيفية التعارف والتحية باللغة الإنجليزية',
          objectives: ['استخدام عبارات التحية', 'التعريف بالنفس', 'السؤال عن الاسم والعمر'],
          vocabulary: ['Hello', 'Good morning', 'My name is', 'How old are you?']
        },
        {
          id: 2,
          title: 'الدرس الثاني: أفراد العائلة',
          unit: 'Unit 1',
          content: 'تعلم أسماء أفراد العائلة والعلاقات الأسرية',
          objectives: ['معرفة أسماء أفراد العائلة', 'استخدام الضمائر', 'وصف العائلة'],
          vocabulary: ['Father', 'Mother', 'Brother', 'Sister', 'Grandfather', 'Grandmother']
        }
      ]
    },
    ict: {
      name: 'تكنولوجيا المعلومات',
      description: 'أساسيات الكمبيوتر والتكنولوجيا للصف الخامس',
      icon: '💻',
      color: 'from-orange-400 to-orange-600',
      books: [
        {
          id: 1,
          title: 'كتاب تكنولوجيا المعلومات - الصف الخامس',
          type: 'كتاب الوزارة الرسمي',
          pages: 100,
          size: '20 MB',
          downloadUrl: '#',
          previewUrl: '#'
        },
        {
          id: 2,
          title: 'دليل المعلم - ICT',
          type: 'دليل المعلم',
          pages: 60,
          size: '8 MB',
          downloadUrl: '#',
          previewUrl: '#'
        }
      ],
      videos: [
        {
          id: 1,
          title: 'مقدمة في الكمبيوتر - أجزاء الحاسوب',
          description: 'تعرف على أجزاء الكمبيوتر الأساسية ووظائفها',
          duration: '18:20',
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
          youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          topics: ['Hardware', 'Software', 'Input/Output devices']
        },
        {
          id: 2,
          title: 'برنامج الرسام - Paint',
          description: 'تعلم استخدام برنامج الرسام لإنشاء رسومات بسيطة',
          duration: '22:15',
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
          youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          topics: ['Drawing tools', 'Colors', 'Shapes', 'Text']
        }
      ],
      lessons: [
        {
          id: 1,
          title: 'الدرس الأول: مكونات الحاسوب',
          unit: 'الوحدة الأولى',
          content: 'تعرف على المكونات الأساسية للحاسوب',
          objectives: ['التمييز بين المكونات المادية والبرمجية', 'معرفة وظائف كل مكون', 'فهم كيفية عمل الحاسوب'],
          vocabulary: ['Hardware', 'Software', 'CPU', 'RAM', 'Hard Drive']
        }
      ]
    }
  }

  const currentSubject = subjectData[subject as keyof typeof subjectData]

  if (!currentSubject) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">المادة غير متاحة حالياً</h2>
          <p className="text-gray-600 mb-6">نعمل على إضافة المحتوى قريباً</p>
          <Link href={`/levels/${level}/grade/${grade}`} className="btn-primary">
            العودة للصف
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-lg">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Link href={`/levels/${level}/grade/${grade}`} className="flex items-center text-primary-600 hover:text-primary-700">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للصف
            </Link>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="text-3xl">{currentSubject.icon}</div>
              <div>
                <h1 className="text-xl font-bold text-gray-800">{currentSubject.name}</h1>
                <p className="text-gray-600 text-sm">الصف الخامس الابتدائي</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Subject Hero */}
      <section className={`py-16 px-4 bg-gradient-to-r ${currentSubject.color} text-white`}>
        <div className="container mx-auto text-center">
          <div className="text-6xl mb-6">{currentSubject.icon}</div>
          <h2 className="text-4xl font-bold mb-4">{currentSubject.name}</h2>
          <p className="text-xl mb-8 text-white/90">{currentSubject.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
            <div className="bg-white/20 rounded-lg p-4">
              <div className="text-2xl font-bold">{currentSubject.books.length}</div>
              <div className="text-white/80">كتاب متاح</div>
            </div>
            <div className="bg-white/20 rounded-lg p-4">
              <div className="text-2xl font-bold">{currentSubject.videos.length}</div>
              <div className="text-white/80">فيديو تعليمي</div>
            </div>
            <div className="bg-white/20 rounded-lg p-4">
              <div className="text-2xl font-bold">{currentSubject.lessons.length}</div>
              <div className="text-white/80">درس تفاعلي</div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="py-12 px-4">
        <div className="container mx-auto">
          {/* Books Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold mb-6 text-gray-800 flex items-center">
              <FileText className="h-6 w-6 ml-2 text-blue-600" />
              الكتب والمراجع
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {currentSubject.books.map((book) => (
                <div key={book.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 text-white">
                    <h4 className="font-bold text-lg">{book.title}</h4>
                    <p className="text-blue-100 text-sm">{book.type}</p>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-4">
                      <span>{book.pages} صفحة</span>
                      <span>{book.size}</span>
                    </div>
                    
                    <div className="flex gap-2">
                      <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        <Download className="h-4 w-4 inline ml-1" />
                        تحميل
                      </button>
                      <button className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-sm">
                        <ExternalLink className="h-4 w-4 inline ml-1" />
                        معاينة
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Videos Section */}
          <div className="mb-12">
            <h3 className="text-2xl font-bold mb-6 text-gray-800 flex items-center">
              <Play className="h-6 w-6 ml-2 text-green-600" />
              الفيديوهات التعليمية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {currentSubject.videos.map((video) => (
                <div key={video.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="relative">
                    <img 
                      src={video.thumbnail} 
                      alt={video.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <Play className="h-16 w-16 text-white" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                      {video.duration}
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <h4 className="font-bold text-lg mb-2">{video.title}</h4>
                    <p className="text-gray-600 text-sm mb-3">{video.description}</p>
                    
                    <div className="flex flex-wrap gap-1 mb-4">
                      {video.topics.map((topic, index) => (
                        <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                          {topic}
                        </span>
                      ))}
                    </div>
                    
                    <a 
                      href={video.youtubeUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors text-center block"
                    >
                      مشاهدة على YouTube
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Link href={`/levels/${level}/grade/${grade}/subject/${subject}/exercises`}>
              <div className="bg-orange-50 hover:bg-orange-100 p-6 rounded-xl text-center transition-colors cursor-pointer">
                <Award className="h-12 w-12 text-orange-600 mx-auto mb-4" />
                <h4 className="font-bold text-orange-800 mb-2">التمارين التفاعلية</h4>
                <p className="text-orange-600 text-sm">اختبر معلوماتك</p>
              </div>
            </Link>
            
            <Link href={`/levels/${level}/grade/${grade}/subject/${subject}/ai-tutor`}>
              <div className="bg-purple-50 hover:bg-purple-100 p-6 rounded-xl text-center transition-colors cursor-pointer">
                <Brain className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h4 className="font-bold text-purple-800 mb-2">المعلم الذكي</h4>
                <p className="text-purple-600 text-sm">اسأل أي سؤال</p>
              </div>
            </Link>
            
            <div className="bg-blue-50 hover:bg-blue-100 p-6 rounded-xl text-center transition-colors cursor-pointer">
              <Star className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h4 className="font-bold text-blue-800 mb-2">المفضلة</h4>
              <p className="text-blue-600 text-sm">احفظ الدروس المهمة</p>
            </div>
            
            <div className="bg-green-50 hover:bg-green-100 p-6 rounded-xl text-center transition-colors cursor-pointer">
              <Clock className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h4 className="font-bold text-green-800 mb-2">جدول المراجعة</h4>
              <p className="text-green-600 text-sm">خطة مذاكرة ذكية</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
