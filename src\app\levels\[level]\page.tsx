'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { ArrowRight, BookOpen, Users, Calculator, Atom, Globe, Monitor, FileText, Play, Brain, Award } from 'lucide-react'

export default function LevelPage() {
  const params = useParams()
  const level = params.level as string

  const levelData = {
    primary: {
      name: 'المرحلة الابتدائية',
      description: 'تعلم أساسيات المعرفة مع محتوى مناسب للأطفال',
      grades: [
        { id: 1, name: 'الصف الأول الابتدائي', students: '6-7 سنوات' },
        { id: 2, name: 'الصف الثاني الابتدائي', students: '7-8 سنوات' },
        { id: 3, name: 'الصف الثالث الابتدائي', students: '8-9 سنوات' },
        { id: 4, name: 'الصف الرابع الابتدائي', students: '9-10 سنوات' },
        { id: 5, name: 'الصف الخامس الابتدائي', students: '10-11 سنة' },
        { id: 6, name: 'الصف السادس الابتدائي', students: '11-12 سنة' },
      ]
    },
    preparatory: {
      name: 'المرحلة الإعدادية',
      description: 'بناء المعرفة المتوسطة وتطوير المهارات الأكاديمية',
      grades: [
        { id: 1, name: 'الصف الأول الإعدادي', students: '12-13 سنة' },
        { id: 2, name: 'الصف الثاني الإعدادي', students: '13-14 سنة' },
        { id: 3, name: 'الصف الثالث الإعدادي', students: '14-15 سنة' },
      ]
    },
    secondary: {
      name: 'المرحلة الثانوية',
      description: 'التحضير للجامعة والتخصص الأكاديمي',
      grades: [
        { id: 1, name: 'الصف الأول الثانوي', students: '15-16 سنة' },
        { id: 2, name: 'الصف الثاني الثانوي', students: '16-17 سنة' },
        { id: 3, name: 'الصف الثالث الثانوي', students: '17-18 سنة' },
      ]
    }
  }

  const subjects = [
    {
      id: 'arabic',
      name: 'اللغة العربية',
      icon: FileText,
      color: 'bg-red-500',
      gradient: 'from-red-400 to-red-600',
      description: 'قواعد اللغة والأدب والتعبير'
    },
    {
      id: 'math',
      name: 'الرياضيات',
      icon: Calculator,
      color: 'bg-blue-500',
      gradient: 'from-blue-400 to-blue-600',
      description: 'الحساب والجبر والهندسة'
    },
    {
      id: 'science',
      name: 'العلوم',
      icon: Atom,
      color: 'bg-green-500',
      gradient: 'from-green-400 to-green-600',
      description: 'الفيزياء والكيمياء والأحياء'
    },
    {
      id: 'english',
      name: 'اللغة الإنجليزية',
      icon: Globe,
      color: 'bg-purple-500',
      gradient: 'from-purple-400 to-purple-600',
      description: 'قواعد ومحادثة ومفردات'
    },
    {
      id: 'ict',
      name: 'تكنولوجيا المعلومات',
      icon: Monitor,
      color: 'bg-orange-500',
      gradient: 'from-orange-400 to-orange-600',
      description: 'الكمبيوتر والبرمجة والتكنولوجيا'
    }
  ]

  const currentLevel = levelData[level as keyof typeof levelData]

  if (!currentLevel) {
    return <div>المرحلة غير موجودة</div>
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-lg">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center text-primary-600 hover:text-primary-700">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للرئيسية
            </Link>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <BookOpen className="h-8 w-8 text-primary-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-800">{currentLevel.name}</h1>
                <p className="text-gray-600 text-sm">مدرسة White Land</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Level Info */}
      <section className="py-12 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">{currentLevel.name}</h2>
          <p className="text-xl text-gray-600 mb-8">{currentLevel.description}</p>
        </div>
      </section>

      {/* Grades Selection */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8 text-gray-800">اختر الصف الدراسي</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {currentLevel.grades.map((grade) => (
              <Link key={grade.id} href={`/levels/${level}/grade/${grade.id}`}>
                <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-gray-100 hover:border-primary-200 cursor-pointer">
                  <div className="p-6">
                    <div className="bg-gradient-to-r from-primary-500 to-purple-600 p-4 rounded-lg w-fit mx-auto mb-4">
                      <Users className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-800 text-center mb-2">{grade.name}</h4>
                    <p className="text-gray-600 text-center text-sm mb-4">{grade.students}</p>
                    
                    <div className="flex justify-center">
                      <span className="text-primary-600 font-semibold text-sm">عرض المواد →</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Subjects Preview */}
      <section className="py-12 px-4 bg-white">
        <div className="container mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8 text-gray-800">المواد الدراسية المتاحة</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            {subjects.map((subject) => (
              <div key={subject.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div className={`bg-gradient-to-r ${subject.gradient} p-6 rounded-t-xl`}>
                  <subject.icon className="h-12 w-12 text-white mx-auto" />
                </div>
                
                <div className="p-4">
                  <h4 className="font-bold text-lg text-center mb-2">{subject.name}</h4>
                  <p className="text-gray-600 text-sm text-center">{subject.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-12 px-4">
        <div className="container mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8 text-gray-800">ما ستجده في كل مادة</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 p-4 rounded-full w-fit mx-auto mb-4">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <h4 className="font-bold mb-2">كتب PDF</h4>
              <p className="text-gray-600 text-sm">كتب المنهج الرسمي والمراجع الإضافية</p>
            </div>
            
            <div className="text-center">
              <div className="bg-green-100 p-4 rounded-full w-fit mx-auto mb-4">
                <Play className="h-8 w-8 text-green-600" />
              </div>
              <h4 className="font-bold mb-2">فيديوهات تعليمية</h4>
              <p className="text-gray-600 text-sm">شروحات مرئية مبسطة لكل درس</p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-100 p-4 rounded-full w-fit mx-auto mb-4">
                <Brain className="h-8 w-8 text-purple-600" />
              </div>
              <h4 className="font-bold mb-2">مساعد ذكي</h4>
              <p className="text-gray-600 text-sm">ذكاء اصطناعي للإجابة على الأسئلة</p>
            </div>
            
            <div className="text-center">
              <div className="bg-orange-100 p-4 rounded-full w-fit mx-auto mb-4">
                <Award className="h-8 w-8 text-orange-600" />
              </div>
              <h4 className="font-bold mb-2">اختبارات تفاعلية</h4>
              <p className="text-gray-600 text-sm">تمارين واختبارات لتقييم المستوى</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
