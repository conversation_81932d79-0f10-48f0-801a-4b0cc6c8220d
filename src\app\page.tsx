'use client'

import { useState } from 'react'
import Link from 'next/link'
import { BookOpen, GraduationCap, Users, Brain, Search, Star, Play, FileText, Award } from 'lucide-react'

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState('')

  const educationLevels = [
    {
      id: 'primary',
      name: 'المرحلة الابتدائية',
      description: 'من الصف الأول إلى السادس الابتدائي',
      icon: BookOpen,
      color: 'bg-blue-500',
      gradient: 'from-blue-400 to-blue-600',
      grades: ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس']
    },
    {
      id: 'preparatory',
      name: 'المرحلة الإعدادية',
      description: 'من الصف الأول إلى الثالث الإعدادي',
      icon: GraduationCap,
      color: 'bg-green-500',
      gradient: 'from-green-400 to-green-600',
      grades: ['الأول الإعدادي', 'الثاني الإعدادي', 'الثالث الإعدادي']
    },
    {
      id: 'secondary',
      name: 'المرحلة الثانوية',
      description: 'من الصف الأول إلى الثالث الثانوي',
      icon: Users,
      color: 'bg-purple-500',
      gradient: 'from-purple-400 to-purple-600',
      grades: ['الأول الثانوي', 'الثاني الثانوي', 'الثالث الثانوي']
    }
  ]

  const features = [
    {
      icon: FileText,
      title: 'كتب PDF تفاعلية',
      description: 'جميع الكتب المدرسية بصيغة PDF عالية الجودة'
    },
    {
      icon: Play,
      title: 'فيديوهات تعليمية',
      description: 'شروحات مرئية مبسطة لجميع الدروس'
    },
    {
      icon: Brain,
      title: 'ذكاء اصطناعي',
      description: 'مساعد ذكي للإجابة على الأسئلة وتوليد التمارين'
    },
    {
      icon: Award,
      title: 'اختبارات تفاعلية',
      description: 'تمارين واختبارات قصيرة لتقييم مستوى الطالب'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white shadow-lg border-b-4 border-primary-500">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-primary-500 to-purple-600 p-3 rounded-xl">
                <BookOpen className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gradient">مكتبتي الذكية</h1>
                <p className="text-gray-600 text-sm">مدرسة White Land</p>
              </div>
            </div>
            
            {/* Search Bar */}
            <div className="relative max-w-md w-full">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="ابحث عن مادة أو درس..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6">
            مرحباً بك في 
            <span className="text-gradient"> مكتبتك الذكية</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            مكتبة إلكترونية شاملة تحتوي على جميع المواد الدراسية لكل المراحل التعليمية
            مع تقنيات الذكاء الاصطناعي لتجربة تعلم متميزة
          </p>
          
          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="bg-gradient-to-r from-primary-500 to-purple-600 p-3 rounded-lg w-fit mx-auto mb-4">
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="font-bold text-lg mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Education Levels */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <h3 className="text-3xl font-bold text-center mb-12 text-gray-800">
            اختر المرحلة الدراسية
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {educationLevels.map((level) => (
              <Link key={level.id} href={`/levels/${level.id}`}>
                <div className="group cursor-pointer">
                  <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-2 border-gray-100 hover:border-primary-200">
                    <div className={`bg-gradient-to-r ${level.gradient} p-8 rounded-t-2xl`}>
                      <level.icon className="h-16 w-16 text-white mx-auto mb-4" />
                      <h4 className="text-2xl font-bold text-white text-center">{level.name}</h4>
                    </div>
                    
                    <div className="p-6">
                      <p className="text-gray-600 text-center mb-6">{level.description}</p>
                      
                      <div className="space-y-2">
                        <h5 className="font-semibold text-gray-800 text-center mb-3">الصفوف المتاحة:</h5>
                        <div className="grid grid-cols-2 gap-2">
                          {level.grades.map((grade, index) => (
                            <div key={index} className="bg-gray-50 rounded-lg p-2 text-center text-sm font-medium text-gray-700">
                              {grade}
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="mt-6 text-center">
                        <span className="inline-flex items-center text-primary-600 font-semibold group-hover:text-primary-700">
                          استكشف المحتوى
                          <Star className="mr-2 h-4 w-4" />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-4">
            <BookOpen className="h-6 w-6 ml-2" />
            <span className="text-lg font-semibold">مكتبتي الذكية - مدرسة White Land</span>
          </div>
          <p className="text-gray-400">
            © 2024 جميع الحقوق محفوظة - تم التطوير بتقنيات حديثة لخدمة التعليم
          </p>
        </div>
      </footer>
    </div>
  )
}
