'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { ArrowRight, Send, Brain, User, Bot, Lightbulb, BookOpen, HelpCircle, Sparkles } from 'lucide-react'

interface Message {
  id: number
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}

export default function AITutorPage() {
  const params = useParams()
  const level = params.level as string
  const grade = params.grade as string
  const subject = params.subject as string

  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      type: 'ai',
      content: subject === 'english' 
        ? 'مرحباً! أنا معلمك الذكي للغة الإنجليزية. يمكنني مساعدتك في فهم القواعد، المفردات، والمحادثة. ما الذي تريد تعلمه اليوم؟'
        : 'مرحباً! أنا معلمك الذكي لتكنولوجيا المعلومات. يمكنني شرح مفاهيم الكمبيوتر والبرمجة بطريقة مبسطة. كيف يمكنني مساعدتك؟',
      timestamp: new Date()
    }
  ])
  
  const [inputMessage, setInputMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // أمثلة على الأسئلة المقترحة
  const suggestedQuestions = subject === 'english' ? [
    'كيف أستخدم الضمائر الشخصية؟',
    'ما الفرق بين a و an؟',
    'علمني أسماء أفراد العائلة',
    'كيف أكون جملة بالإنجليزية؟',
    'ما معنى كلمة Hello؟'
  ] : [
    'ما هي أجزاء الحاسوب الأساسية؟',
    'كيف يعمل المعالج؟',
    'ما الفرق بين Hardware و Software؟',
    'كيف أستخدم برنامج Paint؟',
    'ما هي أجهزة الإدخال والإخراج؟'
  ]

  // محاكاة الذكاء الاصطناعي - في التطبيق الحقيقي ستكون API call
  const generateAIResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase()
    
    if (subject === 'english') {
      if (lowerMessage.includes('ضمائر') || lowerMessage.includes('pronouns')) {
        return `الضمائر الشخصية في الإنجليزية هي:
        
📝 **الضمائر الأساسية:**
• I (أنا) - للمتكلم
• You (أنت/أنتم) - للمخاطب
• He (هو) - للمذكر الغائب
• She (هي) - للمؤنث الغائب
• It (هو/هي) - للجماد
• We (نحن) - للجمع المتكلم
• They (هم/هن) - للجمع الغائب

🌟 **أمثلة:**
• I am a student (أنا طالب)
• You are my friend (أنت صديقي)
• He is tall (هو طويل)

هل تريد المزيد من الأمثلة؟`
      }
      
      if (lowerMessage.includes('a') && lowerMessage.includes('an')) {
        return `الفرق بين A و AN:

📚 **القاعدة الأساسية:**
• **A** - تستخدم قبل الكلمات التي تبدأ بصوت ساكن
• **AN** - تستخدم قبل الكلمات التي تبدأ بصوت متحرك

🎯 **أمثلة على A:**
• A book (كتاب)
• A car (سيارة)
• A student (طالب)

🎯 **أمثلة على AN:**
• An apple (تفاحة)
• An elephant (فيل)
• An orange (برتقالة)

💡 **تذكر:** المهم هو الصوت وليس الحرف!`
      }
      
      if (lowerMessage.includes('عائلة') || lowerMessage.includes('family')) {
        return `أفراد العائلة بالإنجليزية:

👨‍👩‍👧‍👦 **العائلة الأساسية:**
• Father / Dad (أب)
• Mother / Mom (أم)
• Brother (أخ)
• Sister (أخت)
• Son (ابن)
• Daughter (ابنة)

👴👵 **الأجداد:**
• Grandfather / Grandpa (جد)
• Grandmother / Grandma (جدة)

🎵 **أغنية للحفظ:**
"Father, Mother, Sister, Brother
Hand in hand with one another!"

هل تريد تعلم المزيد من أفراد العائلة؟`
      }
    } else {
      if (lowerMessage.includes('أجزاء') || lowerMessage.includes('مكونات') || lowerMessage.includes('hardware')) {
        return `أجزاء الحاسوب الأساسية:

🖥️ **المكونات الرئيسية:**

**1. المعالج (CPU):**
• عقل الحاسوب
• يقوم بمعالجة جميع العمليات

**2. الذاكرة (RAM):**
• تخزن البيانات مؤقتاً
• كلما زادت كلما كان الحاسوب أسرع

**3. القرص الصلب (Hard Drive):**
• يحفظ الملفات والبرامج
• التخزين الدائم

**4. اللوحة الأم (Motherboard):**
• تربط جميع الأجزاء ببعضها

🔌 **أجهزة الإدخال والإخراج:**
• الشاشة، لوحة المفاتيح، الفأرة، السماعات

هل تريد معرفة المزيد عن أي جزء؟`
      }
      
      if (lowerMessage.includes('معالج') || lowerMessage.includes('cpu')) {
        return `المعالج (CPU) - عقل الحاسوب:

🧠 **ما هو المعالج؟**
• CPU تعني Central Processing Unit
• هو الجزء الذي يفكر في الحاسوب
• يقوم بجميع العمليات الحسابية

⚡ **كيف يعمل؟**
1. يستقبل التعليمات من البرامج
2. يفهم هذه التعليمات
3. ينفذها بسرعة عالية جداً
4. يرسل النتائج للأجزاء الأخرى

🏃‍♂️ **السرعة:**
• يقاس بوحدة الجيجاهرتز (GHz)
• كلما زادت السرعة، كان الحاسوب أسرع

🔥 **معلومة مثيرة:**
المعالج الحديث يمكنه تنفيذ مليارات العمليات في الثانية الواحدة!`
      }
      
      if (lowerMessage.includes('paint') || lowerMessage.includes('رسام')) {
        return `برنامج الرسام (Paint):

🎨 **ما هو Paint؟**
• برنامج بسيط للرسم والتلوين
• موجود في جميع أجهزة Windows
• مناسب للمبتدئين

🛠️ **الأدوات الأساسية:**
• **القلم:** للرسم الحر
• **الفرشاة:** للرسم بخطوط عريضة
• **الممحاة:** لمسح الأخطاء
• **دلو الطلاء:** لتعبئة المساحات بالألوان
• **الأشكال:** مربعات، دوائر، خطوط

🌈 **الألوان:**
• يمكنك اختيار أي لون تريده
• امزج الألوان لتحصل على ألوان جديدة

💡 **نصيحة:** ابدأ برسمة بسيطة مثل بيت أو شمس!`
      }
    }
    
    return `شكراً لسؤالك! هذا سؤال رائع. دعني أساعدك بطريقة مبسطة:

${userMessage.includes('؟') ? 'سأحتاج لمزيد من التفاصيل لأعطيك إجابة دقيقة.' : 'يمكنني شرح هذا الموضوع بالتفصيل.'}

💡 **اقتراحات للمساعدة:**
• اطرح سؤالاً أكثر تحديداً
• استخدم الأسئلة المقترحة أدناه
• أخبرني ما الجزء الذي لا تفهمه

هل يمكنك إعادة صياغة سؤالك بطريقة أخرى؟`
  }

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return

    const userMessage: Message = {
      id: messages.length + 1,
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsTyping(true)

    // محاكاة وقت التفكير
    setTimeout(() => {
      const aiResponse: Message = {
        id: messages.length + 2,
        type: 'ai',
        content: generateAIResponse(inputMessage),
        timestamp: new Date()
      }
      
      setMessages(prev => [...prev, aiResponse])
      setIsTyping(false)
    }, 1500)
  }

  const handleSuggestedQuestion = (question: string) => {
    setInputMessage(question)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-lg border-b-4 border-purple-500">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Link href={`/levels/${level}/grade/${grade}/subject/${subject}`} className="flex items-center text-purple-600 hover:text-purple-700">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للمادة
            </Link>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-purple-500 to-blue-600 p-3 rounded-xl">
                <Brain className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-800">المعلم الذكي</h1>
                <p className="text-gray-600 text-sm">
                  {subject === 'english' ? 'مساعد اللغة الإنجليزية' : 'مساعد تكنولوجيا المعلومات'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Chat Container */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            {/* Chat Header */}
            <div className="bg-gradient-to-r from-purple-500 to-blue-600 p-6 text-white">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-white/20 p-3 rounded-full">
                  <Sparkles className="h-6 w-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold">معلمك الذكي جاهز للمساعدة!</h2>
                  <p className="text-purple-100">اسأل أي سؤال وسأجيبك بطريقة مبسطة ومفهومة</p>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="h-96 overflow-y-auto p-6 space-y-4">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-start' : 'justify-end'}`}>
                  <div className={`flex items-start space-x-3 space-x-reverse max-w-3xl ${message.type === 'user' ? 'flex-row' : 'flex-row-reverse'}`}>
                    <div className={`p-2 rounded-full ${message.type === 'user' ? 'bg-blue-100' : 'bg-purple-100'}`}>
                      {message.type === 'user' ? (
                        <User className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Bot className="h-5 w-5 text-purple-600" />
                      )}
                    </div>
                    
                    <div className={`p-4 rounded-2xl ${
                      message.type === 'user' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      <div className="whitespace-pre-line">{message.content}</div>
                      <div className={`text-xs mt-2 ${message.type === 'user' ? 'text-blue-100' : 'text-gray-500'}`}>
                        {message.timestamp.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {isTyping && (
                <div className="flex justify-end">
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <div className="p-2 rounded-full bg-purple-100">
                      <Bot className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="bg-gray-100 p-4 rounded-2xl">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Suggested Questions */}
            <div className="border-t bg-gray-50 p-4">
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <HelpCircle className="h-4 w-4 ml-1" />
                أسئلة مقترحة:
              </h3>
              <div className="flex flex-wrap gap-2">
                {suggestedQuestions.map((question, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestedQuestion(question)}
                    className="bg-white hover:bg-purple-50 text-purple-700 px-3 py-2 rounded-lg text-sm border border-purple-200 hover:border-purple-300 transition-colors"
                  >
                    {question}
                  </button>
                ))}
              </div>
            </div>

            {/* Input */}
            <div className="border-t p-4">
              <div className="flex space-x-4 space-x-reverse">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="اكتب سؤالك هنا..."
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isTyping}
                  className="bg-purple-600 text-white p-3 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div className="bg-white p-6 rounded-xl shadow-lg text-center">
              <Lightbulb className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="font-bold text-lg mb-2">شرح مبسط</h3>
              <p className="text-gray-600 text-sm">أشرح المفاهيم الصعبة بطريقة سهلة ومفهومة</p>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-lg text-center">
              <BookOpen className="h-12 w-12 text-blue-500 mx-auto mb-4" />
              <h3 className="font-bold text-lg mb-2">أمثلة عملية</h3>
              <p className="text-gray-600 text-sm">أقدم أمثلة من الحياة اليومية لتسهيل الفهم</p>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-lg text-center">
              <HelpCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="font-bold text-lg mb-2">إجابات فورية</h3>
              <p className="text-gray-600 text-sm">متاح 24/7 للإجابة على جميع أسئلتك</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
