'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { ArrowRight, Award, Clock, CheckCircle, XCircle, RotateCcw, Star, Trophy } from 'lucide-react'

interface Question {
  id: number
  type: 'multiple-choice' | 'true-false' | 'fill-blank'
  question: string
  options?: string[]
  correctAnswer: string | number
  explanation: string
  points: number
}

export default function ExercisesPage() {
  const params = useParams()
  const level = params.level as string
  const grade = params.grade as string
  const subject = params.subject as string

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<{[key: number]: any}>({})
  const [showResults, setShowResults] = useState(false)
  const [timeLeft, setTimeLeft] = useState(600) // 10 minutes

  // أسئلة تفاعلية للصف الخامس - اللغة الإنجليزية
  const englishQuestions: Question[] = [
    {
      id: 1,
      type: 'multiple-choice',
      question: 'What is the correct greeting in the morning?',
      options: ['Good night', 'Good morning', 'Good evening', 'Good afternoon'],
      correctAnswer: 1,
      explanation: 'Good morning is used to greet someone in the morning hours.',
      points: 10
    },
    {
      id: 2,
      type: 'true-false',
      question: 'The word "Father" means "أب" in Arabic.',
      correctAnswer: 'true',
      explanation: 'Yes, Father means أب in Arabic.',
      points: 5
    },
    {
      id: 3,
      type: 'multiple-choice',
      question: 'Choose the correct sentence:',
      options: ['I am student', 'I am a student', 'I am an student', 'I student'],
      correctAnswer: 1,
      explanation: 'We use "a" before consonant sounds. "Student" starts with "st" which is a consonant sound.',
      points: 15
    },
    {
      id: 4,
      type: 'fill-blank',
      question: 'Complete: My _____ is Ahmed.',
      correctAnswer: 'name',
      explanation: 'The correct word is "name" - My name is Ahmed.',
      points: 10
    }
  ]

  // أسئلة تكنولوجيا المعلومات
  const ictQuestions: Question[] = [
    {
      id: 1,
      type: 'multiple-choice',
      question: 'ما هو الجزء المسؤول عن معالجة البيانات في الحاسوب؟',
      options: ['الشاشة', 'المعالج (CPU)', 'الفأرة', 'لوحة المفاتيح'],
      correctAnswer: 1,
      explanation: 'المعالج (CPU) هو الجزء المسؤول عن معالجة جميع البيانات والعمليات في الحاسوب.',
      points: 10
    },
    {
      id: 2,
      type: 'true-false',
      question: 'الفأرة هي من أجهزة الإدخال.',
      correctAnswer: 'true',
      explanation: 'نعم، الفأرة تستخدم لإدخال الأوامر والتحكم في الحاسوب.',
      points: 5
    },
    {
      id: 3,
      type: 'multiple-choice',
      question: 'أي من هذه البرامج يستخدم للرسم؟',
      options: ['Microsoft Word', 'Paint', 'Calculator', 'Notepad'],
      correctAnswer: 1,
      explanation: 'برنامج Paint يستخدم لإنشاء الرسومات والصور.',
      points: 15
    }
  ]

  const questions = subject === 'english' ? englishQuestions : ictQuestions
  const currentQuestion = questions[currentQuestionIndex]

  const handleAnswerSelect = (answer: any) => {
    setSelectedAnswers({
      ...selectedAnswers,
      [currentQuestion.id]: answer
    })
  }

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    } else {
      setShowResults(true)
    }
  }

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }

  const calculateScore = () => {
    let totalScore = 0
    let correctAnswers = 0

    questions.forEach(question => {
      const userAnswer = selectedAnswers[question.id]
      if (userAnswer === question.correctAnswer) {
        totalScore += question.points
        correctAnswers++
      }
    })

    return { totalScore, correctAnswers, totalQuestions: questions.length }
  }

  const resetQuiz = () => {
    setCurrentQuestionIndex(0)
    setSelectedAnswers({})
    setShowResults(false)
    setTimeLeft(600)
  }

  if (showResults) {
    const { totalScore, correctAnswers, totalQuestions } = calculateScore()
    const percentage = Math.round((correctAnswers / totalQuestions) * 100)

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <header className="bg-white shadow-lg">
          <div className="container mx-auto px-4 py-6">
            <Link href={`/levels/${level}/grade/${grade}/subject/${subject}`} className="flex items-center text-primary-600 hover:text-primary-700">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للمادة
            </Link>
          </div>
        </header>

        <div className="container mx-auto px-4 py-12">
          <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-xl p-8 text-center">
            <div className="mb-8">
              {percentage >= 80 ? (
                <Trophy className="h-20 w-20 text-yellow-500 mx-auto mb-4" />
              ) : percentage >= 60 ? (
                <Star className="h-20 w-20 text-blue-500 mx-auto mb-4" />
              ) : (
                <Award className="h-20 w-20 text-gray-500 mx-auto mb-4" />
              )}
              
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                {percentage >= 80 ? 'ممتاز! 🎉' : percentage >= 60 ? 'جيد جداً! 👏' : 'يمكنك التحسن! 💪'}
              </h2>
              
              <div className="text-6xl font-bold text-primary-600 mb-2">{percentage}%</div>
              <p className="text-gray-600">
                أجبت على {correctAnswers} من {totalQuestions} أسئلة بشكل صحيح
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{correctAnswers}</div>
                <div className="text-green-800 text-sm">إجابات صحيحة</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{totalQuestions - correctAnswers}</div>
                <div className="text-red-800 text-sm">إجابات خاطئة</div>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{totalScore}</div>
                <div className="text-blue-800 text-sm">النقاط المكتسبة</div>
              </div>
            </div>

            <div className="flex gap-4 justify-center">
              <button 
                onClick={resetQuiz}
                className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
              >
                <RotateCcw className="h-5 w-5 ml-2" />
                إعادة المحاولة
              </button>
              
              <Link 
                href={`/levels/${level}/grade/${grade}/subject/${subject}`}
                className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
              >
                العودة للمادة
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-lg">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Link href={`/levels/${level}/grade/${grade}/subject/${subject}`} className="flex items-center text-primary-600 hover:text-primary-700">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للمادة
            </Link>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <Clock className="h-5 w-5 text-gray-600" />
              <span className="text-gray-600">
                {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">
              السؤال {currentQuestionIndex + 1} من {questions.length}
            </span>
            <span className="text-sm text-gray-600">
              {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Question */}
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                  {currentQuestion.points} نقاط
                </span>
                <span className="text-gray-500 text-sm">
                  {currentQuestion.type === 'multiple-choice' ? 'اختر الإجابة الصحيحة' : 
                   currentQuestion.type === 'true-false' ? 'صح أم خطأ' : 'أكمل الفراغ'}
                </span>
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                {currentQuestion.question}
              </h2>
            </div>

            {/* Answer Options */}
            <div className="mb-8">
              {currentQuestion.type === 'multiple-choice' && currentQuestion.options && (
                <div className="space-y-3">
                  {currentQuestion.options.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => handleAnswerSelect(index)}
                      className={`w-full p-4 text-right rounded-lg border-2 transition-all duration-200 ${
                        selectedAnswers[currentQuestion.id] === index
                          ? 'border-primary-500 bg-primary-50 text-primary-800'
                          : 'border-gray-200 hover:border-gray-300 text-gray-700'
                      }`}
                    >
                      <span className="font-medium">{String.fromCharCode(65 + index)}. </span>
                      {option}
                    </button>
                  ))}
                </div>
              )}

              {currentQuestion.type === 'true-false' && (
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => handleAnswerSelect('true')}
                    className={`p-6 rounded-lg border-2 transition-all duration-200 ${
                      selectedAnswers[currentQuestion.id] === 'true'
                        ? 'border-green-500 bg-green-50 text-green-800'
                        : 'border-gray-200 hover:border-gray-300 text-gray-700'
                    }`}
                  >
                    <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                    <div className="font-bold">صح</div>
                  </button>
                  
                  <button
                    onClick={() => handleAnswerSelect('false')}
                    className={`p-6 rounded-lg border-2 transition-all duration-200 ${
                      selectedAnswers[currentQuestion.id] === 'false'
                        ? 'border-red-500 bg-red-50 text-red-800'
                        : 'border-gray-200 hover:border-gray-300 text-gray-700'
                    }`}
                  >
                    <XCircle className="h-8 w-8 mx-auto mb-2" />
                    <div className="font-bold">خطأ</div>
                  </button>
                </div>
              )}

              {currentQuestion.type === 'fill-blank' && (
                <div>
                  <input
                    type="text"
                    placeholder="اكتب إجابتك هنا..."
                    value={selectedAnswers[currentQuestion.id] || ''}
                    onChange={(e) => handleAnswerSelect(e.target.value)}
                    className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-primary-500 focus:outline-none text-lg"
                  />
                </div>
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between">
              <button
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
                className="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                السؤال السابق
              </button>
              
              <button
                onClick={handleNextQuestion}
                disabled={!selectedAnswers[currentQuestion.id]}
                className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {currentQuestionIndex === questions.length - 1 ? 'إنهاء الاختبار' : 'السؤال التالي'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
