<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبتي الذكية - SmartEdu Library</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .status {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .feature-desc {
            color: #666;
            line-height: 1.6;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .demo-card {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .demo-card:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }

        .demo-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .demo-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .demo-desc {
            color: #666;
            font-size: 0.9rem;
        }

        .tech-stack {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .tech-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }

        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .tech-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 30px;
        }

        .next-steps {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .steps-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }

        .steps-list {
            list-style: none;
            padding: 0;
        }

        .steps-list li {
            background: #f8fafc;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }

        .steps-list li::before {
            content: "✓";
            color: #22c55e;
            font-weight: bold;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }

            .features {
                grid-template-columns: 1fr;
            }

            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📚</div>
            <h1 class="title">مكتبتي الذكية</h1>
            <h2 class="subtitle">SmartEdu Library - مدرسة White Land</h2>
            <div class="status">✅ تم إنشاء المشروع بنجاح!</div>
            <p>مكتبة إلكترونية ذكية شاملة لجميع المراحل التعليمية مع تقنيات الذكاء الاصطناعي</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">📖</div>
                <h3 class="feature-title">محتوى تعليمي شامل</h3>
                <p class="feature-desc">كتب PDF، فيديوهات تعليمية، وملخصات ذكية لجميع المواد الدراسية</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3 class="feature-title">ذكاء اصطناعي متطور</h3>
                <p class="feature-desc">معلم ذكي يجيب على الأسئلة ويولد التمارين ويقترح خطط المذاكرة</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">تمارين تفاعلية</h3>
                <p class="feature-desc">اختبارات قصيرة وتمارين متنوعة مع نظام تقييم ذكي</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3 class="feature-title">تصميم متجاوب</h3>
                <p class="feature-desc">واجهة عربية سهلة الاستخدام تعمل على جميع الأجهزة</p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">المثال العملي المطبق - الصف الخامس الابتدائي</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <div class="demo-icon">🇬🇧</div>
                    <h3 class="demo-name">اللغة الإنجليزية</h3>
                    <p class="demo-desc">منهج Connect Plus مع فيديوهات وتمارين تفاعلية</p>
                </div>

                <div class="demo-card">
                    <div class="demo-icon">💻</div>
                    <h3 class="demo-name">تكنولوجيا المعلومات</h3>
                    <p class="demo-desc">أساسيات الكمبيوتر وبرنامج Paint مع شروحات مبسطة</p>
                </div>

                <div class="demo-card">
                    <div class="demo-icon">📚</div>
                    <h3 class="demo-name">كتب PDF</h3>
                    <p class="demo-desc">كتب الوزارة والمعاصر بجودة عالية</p>
                </div>

                <div class="demo-card">
                    <div class="demo-icon">🎥</div>
                    <h3 class="demo-name">فيديوهات تعليمية</h3>
                    <p class="demo-desc">شروحات مرئية من YouTube مختارة بعناية</p>
                </div>

                <div class="demo-card">
                    <div class="demo-icon">🧠</div>
                    <h3 class="demo-name">المعلم الذكي</h3>
                    <p class="demo-desc">شات بوت ذكي للإجابة على الأسئلة</p>
                </div>

                <div class="demo-card">
                    <div class="demo-icon">🏆</div>
                    <h3 class="demo-name">اختبارات تفاعلية</h3>
                    <p class="demo-desc">أسئلة متنوعة مع نظام تقييم فوري</p>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h2 class="tech-title">التقنيات المستخدمة</h2>
            <div class="tech-list">
                <span class="tech-item">Next.js 14</span>
                <span class="tech-item">React 18</span>
                <span class="tech-item">TypeScript</span>
                <span class="tech-item">Tailwind CSS</span>
                <span class="tech-item">Lucide React</span>
                <span class="tech-item">خطوط عربية</span>
            </div>
        </div>

        <div class="next-steps">
            <h2 class="steps-title">خطوات تشغيل المشروع</h2>
            <ul class="steps-list">
                <li>تثبيت Node.js على الجهاز</li>
                <li>تشغيل الأمر: npm install</li>
                <li>تشغيل الأمر: npm run dev</li>
                <li>فتح المتصفح على: http://localhost:3000</li>
                <li>استكشاف المكتبة والمحتوى التعليمي</li>
            </ul>
        </div>

        <div class="footer">
            <p>© 2024 مدرسة White Land - جميع الحقوق محفوظة</p>
            <p>تم التطوير بتقنيات حديثة لخدمة التعليم ❤️</p>
        </div>
    </div>
</body>
</html>