import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'مكتبتي الذكية - SmartEdu Library',
  description: 'مكتبة إلكترونية ذكية شاملة لجميع المراحل التعليمية مع الذكاء الاصطناعي',
  keywords: 'تعليم، مكتبة إلكترونية، ذكاء اصطناعي، مناهج، ابتدائي، إعدادي، ثانوي',
  authors: [{ name: 'White Land School' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'مكتبتي الذكية - SmartEdu Library',
    description: 'مكتبة إلكترونية ذكية شاملة لجميع المراحل التعليمية',
    type: 'website',
    locale: 'ar_EG',
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className="font-arabic antialiased">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
          {children}
        </div>
      </body>
    </html>
  )
}
